#!/usr/bin/env python3
import struct
import numpy as np
from io import BytesIO
from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Optional, Any

# --- SF2 Generator Enumeration (Partial List) ---
# See SF2 specification section 8.1.3
class SF2Generator:
    StartAddrsOffset = 0
    EndAddrsOffset = 1
    StartLoopAddrsOffset = 2
    EndLoopAddrsOffset = 3
    StartAddrsCoarseOffset = 4
    ModLfoToPitch = 5
    VibLfoToPitch = 6
    ModEnvToPitch = 7
    InitialFilterFc = 8
    InitialFilterQ = 9
    ModLfoToFilterFc = 10
    ModEnvToFilterFc = 11
    EndAddrsCoarseOffset = 12
    ModLfoToVolume = 13
    # Unused 14
    ChorusEffectsSend = 15
    ReverbEffectsSend = 16
    Pan = 17
    # Unused 18, 19, 20
    DelayModLFO = 21
    FreqModLFO = 22
    DelayVibLFO = 23
    FreqVibLFO = 24
    DelayModEnv = 25
    AttackModEnv = 26
    HoldModEnv = 27
    DecayModEnv = 28
    SustainModEnv = 29
    ReleaseModEnv = 30
    KeynumToModEnvHold = 31
    KeynumToModEnvDecay = 32
    DelayVolEnv = 33
    AttackVolEnv = 34
    HoldVolEnv = 35
    DecayVolEnv = 36
    SustainVolEnv = 37
    ReleaseVolEnv = 38
    KeynumToVolEnvHold = 39
    KeynumToVolEnvDecay = 40
    Instrument = 41
    # Unused 42
    KeyRange = 43
    VelRange = 44
    StartLoopAddrsCoarseOffset = 45
    Keynum = 46
    Velocity = 47
    InitialAttenuation = 48
    # Unused 49
    EndLoopAddrsCoarseOffset = 50
    CoarseTune = 51
    FineTune = 52
    SampleID = 53
    SampleModes = 54
    # Unused 55
    ScaleTuning = 56
    ExclusiveClass = 57
    OverridingRootKey = 58
    # Unused 59
    EndOper = 60

# --- Data Classes for SF2 Structure ---

@dataclass
class SF2Chunk:
    id: bytes
    size: int
    data_offset: int # Offset in the file where data begins

@dataclass
class SF2GeneratorInfo:
    operator: int
    amount: int # Stored as signed short in file

    def get_amount_range(self) -> Optional[Tuple[int, int]]:
        """Returns the (low, high) byte range if applicable."""
        if self.operator in [SF2Generator.KeyRange, SF2Generator.VelRange]:
            return (self.amount & 0xFF, (self.amount >> 8) & 0xFF)
        return None

@dataclass
class SF2ModulatorInfo:
    # See SF2 spec 8.2.3, 8.3.3, 9.5, 9.6
    # Simplified for now - needs full controller/source/destination mapping
    mod_src_oper: int
    mod_dest_oper: int
    mod_amount: int # Signed short
    mod_amt_src_oper: int
    mod_trans_oper: int

@dataclass
class SF2Zone:
    generators: List[SF2GeneratorInfo] = field(default_factory=list)
    modulators: List[SF2ModulatorInfo] = field(default_factory=list)
    # Helper properties derived from generators
    key_range: Optional[Tuple[int, int]] = None
    vel_range: Optional[Tuple[int, int]] = None
    instrument_id: Optional[int] = None # For preset zones
    sample_id: Optional[int] = None # For instrument zones

    def finalize(self):
        """Calculate helper properties after all generators are added."""
        for gen in self.generators:
            if gen.operator == SF2Generator.KeyRange:
                self.key_range = gen.get_amount_range()
            elif gen.operator == SF2Generator.VelRange:
                self.vel_range = gen.get_amount_range()
            elif gen.operator == SF2Generator.Instrument:
                self.instrument_id = gen.amount
            elif gen.operator == SF2Generator.SampleID:
                self.sample_id = gen.amount

@dataclass
class SF2Instrument:
    name: str
    start_zone_index: int
    num_zones: int
    zones: List[SF2Zone] = field(default_factory=list) # Populated later

@dataclass
class SF2Preset:
    name: str
    preset_num: int
    bank_num: int
    start_zone_index: int
    num_zones: int
    library: int # Unused
    genre: int # Unused
    morphology: int # Unused
    zones: List[SF2Zone] = field(default_factory=list) # Populated later

@dataclass
class SF2SampleHeader:
    name: str
    start: int # Index in smpl data (in samples, not bytes)
    end: int
    loop_start: int
    loop_end: int
    sample_rate: int
    original_pitch: int # MIDI key number (0-127)
    pitch_correction: int # Cents (-128 to 127) -> convert to semitones / 100.0
    sample_link: int # Index to linked sample (e.g., right channel)
    sample_type: int # 1:mono, 2:right, 4:left, 8:linked, 0x8000:ROM

    def get_pitch_correction_semitones(self) -> float:
        # Convert signed byte stored as int to actual signed value
        corr = self.pitch_correction
        if corr >= 128:
            corr -= 256
        return corr / 100.0

@dataclass
class SF2FileInfo:
    version: Tuple[int, int] = (0, 0)
    sound_engine: str = ""
    bank_name: str = ""
    rom_name: str = ""
    rom_version: Tuple[int, int] = (0, 0)
    creation_date: str = ""
    author: str = ""
    product: str = ""
    copyright: str = ""
    comments: str = ""
    tools: str = ""

# --- Main Parser Class ---

class SF2NewParser:
    def __init__(self, filepath: str):
        self.filepath = filepath
        self.file = None
        self.chunks: Dict[bytes, SF2Chunk] = {}
        self.info = SF2FileInfo()
        self.sample_headers: List[SF2SampleHeader] = []
        self.presets: List[SF2Preset] = []
        self.instruments: List[SF2Instrument] = []
        # Raw zone/gen/mod data before being assigned to presets/instruments
        self._preset_zones_raw: List[SF2Zone] = []
        self._instrument_zones_raw: List[SF2Zone] = []
        # Sample data (loaded separately if needed)
        self.samples: Optional[np.ndarray] = None # Holds the entire smpl chunk data

    def _read_chunk_header(self) -> Optional[SF2Chunk]:
        """Reads the 8-byte header (ID + size) of the next chunk."""
        try:
            header_data = self.file.read(8)
            if len(header_data) < 8:
                return None # End of file or incomplete chunk
            chunk_id, chunk_size = struct.unpack('<4sI', header_data)
            return SF2Chunk(id=chunk_id, size=chunk_size, data_offset=self.file.tell())
        except Exception as e:
            print(f"Error reading chunk header: {e}")
            return None

    def _parse_riff_header(self):
        """Parses the main RIFF container."""
        print("Parsing RIFF header...")
        chunk = self._read_chunk_header()
        if not chunk or chunk.id != b'RIFF':
            raise ValueError("Invalid SF2: Missing RIFF chunk.")

        # Check for 'sfbk' type
        sfbk_type = self.file.read(4)
        if sfbk_type != b'sfbk':
            raise ValueError("Invalid SF2: RIFF type is not 'sfbk'.")
        print(f"RIFF chunk found (size: {chunk.size}), type: sfbk")

        # Read LIST chunks within RIFF
        list_end = chunk.data_offset + chunk.size - 4 # -4 for 'sfbk' type read
        while self.file.tell() < list_end:
            list_chunk = self._read_chunk_header()
            if not list_chunk:
                break # Should not happen in valid file before list_end

            if list_chunk.id == b'LIST':
                list_type = self.file.read(4)
                print(f"  Found LIST chunk (size: {list_chunk.size}), type: {list_type.decode('ascii')}")
                if list_type == b'INFO':
                    self._parse_info_list(list_chunk)
                elif list_type == b'sdta':
                    self._parse_sdta_list(list_chunk)
                elif list_type == b'pdta':
                    self._parse_pdta_list(list_chunk)
                else:
                    print(f"    Skipping unknown LIST type: {list_type}")
                    self.file.seek(list_chunk.data_offset + list_chunk.size) # Skip data
            else:
                 print(f"  Skipping unexpected chunk within RIFF: {list_chunk.id}")
                 self.file.seek(list_chunk.data_offset + list_chunk.size) # Skip data

            # Ensure alignment (chunks are padded to even size)
            if self.file.tell() % 2 != 0:
                self.file.seek(1, 1) # Seek 1 byte relative to current pos

    def _parse_info_list(self, list_chunk: SF2Chunk):
        """Parses the INFO LIST chunk."""
        print("    Parsing INFO...")
        end_offset = list_chunk.data_offset + list_chunk.size - 4 # -4 for 'INFO' type read
        while self.file.tell() < end_offset:
            sub_chunk = self._read_chunk_header()
            if not sub_chunk: break

            data = self.file.read(sub_chunk.size).rstrip(b'\x00')
            try:
                data_str = data.decode('ascii') # Or latin-1? Spec is vague.
            except UnicodeDecodeError:
                data_str = data.decode('latin-1', errors='ignore')

            # Store info based on chunk ID (e.g., 'ifil', 'isng', 'INAM', etc.)
            if sub_chunk.id == b'ifil':
                if len(data) >= 4:
                    major, minor = struct.unpack('<HH', data[:4])
                    self.info.version = (major, minor)
                    print(f"      ifil (Version): {major}.{minor}")
            elif sub_chunk.id == b'isng':
                self.info.sound_engine = data_str
                print(f"      isng (Sound Engine): {data_str}")
            elif sub_chunk.id == b'INAM':
                self.info.bank_name = data_str
                print(f"      INAM (Bank Name): {data_str}")
            # Add other INFO sub-chunks as needed (ICRD, IENG, IPRD, ICOP, ICMT, ISFT)
            elif sub_chunk.id == b'ICRD': self.info.creation_date = data_str
            elif sub_chunk.id == b'IENG': self.info.author = data_str
            elif sub_chunk.id == b'IPRD': self.info.product = data_str
            elif sub_chunk.id == b'ICOP': self.info.copyright = data_str
            elif sub_chunk.id == b'ICMT': self.info.comments = data_str
            elif sub_chunk.id == b'ISFT': self.info.tools = data_str
            else:
                print(f"      Skipping unknown INFO sub-chunk: {sub_chunk.id}")

            if self.file.tell() % 2 != 0: self.file.seek(1, 1)

    def _parse_sdta_list(self, list_chunk: SF2Chunk):
        """Parses the sdta LIST chunk (contains sample data)."""
        print("    Parsing sdta...")
        end_offset = list_chunk.data_offset + list_chunk.size - 4 # -4 for 'sdta' type read
        while self.file.tell() < end_offset:
            sub_chunk = self._read_chunk_header()
            if not sub_chunk: break

            if sub_chunk.id == b'smpl':
                print(f"      Found smpl chunk (size: {sub_chunk.size}) at offset {sub_chunk.data_offset}")
                # Store chunk info, but don't read data yet
                self.chunks[b'smpl'] = sub_chunk
                self.file.seek(sub_chunk.data_offset + sub_chunk.size) # Skip data for now
            else:
                print(f"      Skipping unknown sdta sub-chunk: {sub_chunk.id}")
                self.file.seek(sub_chunk.data_offset + sub_chunk.size)

            if self.file.tell() % 2 != 0: self.file.seek(1, 1)

    def _parse_pdta_list(self, list_chunk: SF2Chunk):
        """Parses the pdta LIST chunk (presets, instruments, zones, etc.)."""
        print("    Parsing pdta...")
        end_offset = list_chunk.data_offset + list_chunk.size - 4 # -4 for 'pdta' type read
        while self.file.tell() < end_offset:
            sub_chunk = self._read_chunk_header()
            if not sub_chunk: break

            print(f"      Found pdta sub-chunk: {sub_chunk.id.decode('ascii')} (size: {sub_chunk.size})")
            # Store chunk info, read data later in specific order
            self.chunks[sub_chunk.id] = sub_chunk
            self.file.seek(sub_chunk.data_offset + sub_chunk.size) # Skip data for now

            if self.file.tell() % 2 != 0: self.file.seek(1, 1)

    def _read_pdta_records(self, chunk_id: bytes, record_size: int, format_str: str) -> List[Tuple]:
        """Reads records from a pdta sub-chunk."""
        if chunk_id not in self.chunks:
            print(f"Warning: Required pdta chunk '{chunk_id.decode('ascii')}' not found.")
            return []

        chunk = self.chunks[chunk_id]
        self.file.seek(chunk.data_offset)
        num_records = chunk.size // record_size
        records = []
        print(f"      Reading {num_records} records from {chunk_id.decode('ascii')} (record size: {record_size})")

        if num_records <= 1: # Last record is a terminator
             print(f"      Chunk {chunk_id.decode('ascii')} has only terminator record or is empty.")
             return []

        for i in range(num_records):
            record_data = self.file.read(record_size)
            if len(record_data) < record_size:
                print(f"Warning: Incomplete record {i} in {chunk_id.decode('ascii')}")
                break
            try:
                unpacked_data = struct.unpack(format_str, record_data)
                records.append(unpacked_data)
            except struct.error as e:
                print(f"Error unpacking record {i} in {chunk_id.decode('ascii')}: {e}")
                # Attempt to recover or skip? For now, stop reading this chunk.
                break

        # The last record in each pdta chunk is a terminator and should be ignored
        return records[:-1]

    def _load_pdta_data(self):
        """Loads data from all pdta sub-chunks in the correct order."""
        print("    Loading PDTA sub-chunk data...")

        # 1. Preset Headers (phdr) - Defines presets
        # Format: 20s H H H I I I (name, preset, bank, bagIndex, library, genre, morphology)
        phdr_records = self._read_pdta_records(b'phdr', 38, '<20sHHHIII')
        for name_bytes, preset, bank, bag_idx, lib, genre, morph in phdr_records:
            name = name_bytes.decode('ascii', errors='ignore').strip('\x00')
            self.presets.append(SF2Preset(name, preset, bank, bag_idx, 0, lib, genre, morph)) # num_zones calculated later
        print(f"      Loaded {len(self.presets)} presets.")

        # 2. Preset Zones (pbag) - Links presets to generators/modulators
        # Format: H H (genIndex, modIndex)
        pbag_records = self._read_pdta_records(b'pbag', 4, '<HH')
        # Store temporarily, used to link zones later
        self._pbag_raw = pbag_records
        print(f"      Loaded {len(self._pbag_raw)} preset zone index pairs.")

        # 3. Preset Modulators (pmod) - Defines modulators for preset zones
        # Format: H H h H H (modSrcOper, modDestOper, modAmount, modAmtSrcOper, modTransOper)
        # See SF2 Spec 8.2.3, 9.5 for details on structure
        pmod_records = self._read_pdta_records(b'pmod', 10, '<HHhHH')
        self._pmod_raw = [SF2ModulatorInfo(*rec) for rec in pmod_records]
        print(f"      Loaded {len(self._pmod_raw)} preset modulators.")

        # 4. Preset Generators (pgen) - Defines generators for preset zones
        # Format: H h (genOper, genAmount)
        pgen_records = self._read_pdta_records(b'pgen', 4, '<Hh')
        self._pgen_raw = [SF2GeneratorInfo(*rec) for rec in pgen_records]
        print(f"      Loaded {len(self._pgen_raw)} preset generators.")

        # 5. Instrument Headers (inst) - Defines instruments
        # Format: 20s H (name, bagIndex)
        inst_records = self._read_pdta_records(b'inst', 22, '<20sH')
        for name_bytes, bag_idx in inst_records:
            name = name_bytes.decode('ascii', errors='ignore').strip('\x00')
            self.instruments.append(SF2Instrument(name, bag_idx, 0)) # num_zones calculated later
        print(f"      Loaded {len(self.instruments)} instruments.")

        # 6. Instrument Zones (ibag) - Links instruments to generators/modulators
        # Format: H H (genIndex, modIndex)
        ibag_records = self._read_pdta_records(b'ibag', 4, '<HH')
        self._ibag_raw = ibag_records
        print(f"      Loaded {len(self._ibag_raw)} instrument zone index pairs.")

        # 7. Instrument Modulators (imod) - Defines modulators for instrument zones
        # Format: H H h H H (modSrcOper, modDestOper, modAmount, modAmtSrcOper, modTransOper)
        imod_records = self._read_pdta_records(b'imod', 10, '<HHhHH')
        self._imod_raw = [SF2ModulatorInfo(*rec) for rec in imod_records]
        print(f"      Loaded {len(self._imod_raw)} instrument modulators.")

        # 8. Instrument Generators (igen) - Defines generators for instrument zones
        # Format: H h (genOper, genAmount)
        igen_records = self._read_pdta_records(b'igen', 4, '<Hh')
        self._igen_raw = [SF2GeneratorInfo(*rec) for rec in igen_records]
        print(f"      Loaded {len(self._igen_raw)} instrument generators.")

        # 9. Sample Headers (shdr) - Defines sample metadata
        # Format: 20s I I I I I B b H H (name, start, end, loopStart, loopEnd, sampleRate, originalPitch, pitchCorrection, sampleLink, sampleType)
        shdr_records = self._read_pdta_records(b'shdr', 46, '<20sIIIIIBbHH')
        for name_bytes, start, end, loop_start, loop_end, sr, pitch, corr, link, type_ in shdr_records:
            name = name_bytes.decode('ascii', errors='ignore').strip('\x00')
            self.sample_headers.append(SF2SampleHeader(name, start, end, loop_start, loop_end, sr, pitch, corr, link, type_))
        print(f"      Loaded {len(self.sample_headers)} sample headers.")

        # --- Assemble Zones ---
        print("    Assembling zones...")
        self._preset_zones_raw = self._assemble_zones(self._pbag_raw, self._pgen_raw, self._pmod_raw)
        self._instrument_zones_raw = self._assemble_zones(self._ibag_raw, self._igen_raw, self._imod_raw)

        # --- Link Zones to Presets and Instruments ---
        print("    Linking zones to presets and instruments...")
        self._link_zones_to_items(self.presets, self._preset_zones_raw)
        self._link_zones_to_items(self.instruments, self._instrument_zones_raw)

        # Cleanup raw data
        del self._pbag_raw, self._pgen_raw, self._pmod_raw
        del self._ibag_raw, self._igen_raw, self._imod_raw

    def _assemble_zones(self, bag_records, gen_records, mod_records) -> List[SF2Zone]:
        """Creates Zone objects from raw bag, gen, and mod records."""
        zones = []
        num_bags = len(bag_records)
        for i in range(num_bags):
            gen_start_idx, mod_start_idx = bag_records[i]

            # Determine end index for generators and modulators for this zone
            gen_end_idx = bag_records[i+1][0] if (i + 1) < num_bags else len(gen_records)
            mod_end_idx = bag_records[i+1][1] if (i + 1) < num_bags else len(mod_records)

            zone = SF2Zone(
                generators=gen_records[gen_start_idx:gen_end_idx],
                modulators=mod_records[mod_start_idx:mod_end_idx]
            )
            zone.finalize() # Calculate key/vel range etc.
            zones.append(zone)
        print(f"      Assembled {len(zones)} zones.")
        return zones

    def _link_zones_to_items(self, items: List[Any], zones: List[SF2Zone]):
        """Assigns the correct zones to each Preset or Instrument."""
        num_items = len(items)
        for i in range(num_items):
            item = items[i]
            start_zone_idx = item.start_zone_index
            end_zone_idx = items[i+1].start_zone_index if (i + 1) < num_items else len(zones)
            item.num_zones = end_zone_idx - start_zone_idx
            item.zones = zones[start_zone_idx:end_zone_idx]
            # print(f"      Linked {item.num_zones} zones to {type(item).__name__} '{item.name}' (indices {start_zone_idx}-{end_zone_idx-1})")

    def load_sample_data(self):
        """Loads the actual sample data from the 'smpl' chunk."""
        if self.samples is not None:
            print("Sample data already loaded.")
            return

        if b'smpl' not in self.chunks:
            print("Warning: 'smpl' chunk not found. Cannot load sample data.")
            self.samples = np.array([], dtype=np.int16)
            return

        smpl_chunk = self.chunks[b'smpl']
        print(f"Loading sample data from 'smpl' chunk (size: {smpl_chunk.size} bytes)...")
        
        try:
            self.file = open(self.filepath, 'rb')
            self.file.seek(smpl_chunk.data_offset)
            
            # Determine if we need to read in chunks (for very large files)
            if smpl_chunk.size > 50 * 1024 * 1024:  # If larger than 50MB
                print("Large sample data detected, reading in chunks...")
                chunks = []
                chunk_size = 10 * 1024 * 1024  # 10MB chunks
                bytes_read = 0
                
                while bytes_read < smpl_chunk.size:
                    read_size = min(chunk_size, smpl_chunk.size - bytes_read)
                    chunks.append(self.file.read(read_size))
                    bytes_read += read_size
                    print(f"Read {bytes_read/1024/1024:.1f}MB of {smpl_chunk.size/1024/1024:.1f}MB")
                
                sample_data_bytes = b''.join(chunks)
            else:
                sample_data_bytes = self.file.read(smpl_chunk.size)
            
            # Close the file as we're done reading the sample data
            self.file.close()
            self.file = None
            
            # Convert bytes to numpy array
            self.samples = np.frombuffer(sample_data_bytes, dtype=np.int16)
            print(f"Loaded {self.samples.size} sample points ({self.samples.size * 2} bytes).")
            
        except Exception as e:
            print(f"Error loading sample data: {str(e)}")
            self.samples = np.array([], dtype=np.int16)
            if self.file:
                self.file.close()
                self.file = None

    def get_sample(self, sample_id: int) -> Optional[np.ndarray]:
        """
        Extracts and returns a specific sample as float32 numpy array.
        Handles mono/stereo and normalizes to [-1.0, 1.0].
        
        Args:
            sample_id: Index of the sample to extract
            
        Returns:
            Normalized float32 numpy array with sample data, or None if sample can't be loaded
        """
        if self.samples is None:
            print(f"Loading sample data for sample {sample_id}")
            self.load_sample_data()
        
        if self.samples is None or len(self.samples) == 0:
            print(f"Error: No sample data available for sample {sample_id}")
            return None
        
        if sample_id >= len(self.sample_headers):
            print(f"Error: Invalid sample ID {sample_id}, max is {len(self.sample_headers)-1}")
            return None

        header = self.sample_headers[sample_id]
        
        # Debug info
        # print(f"Sample {sample_id} '{header.name}': start={header.start}, end={header.end}, "
        #       f"loop=[{header.loop_start}:{header.loop_end}], type={header.sample_type}, "
        #       f"rate={header.sample_rate}, pitch={header.original_pitch}")
        
        # Validate sample range
        if header.start >= self.samples.size or header.end > self.samples.size:
            print(f"Warning: Sample {sample_id} ('{header.name}') data range [{header.start}:{header.end}] "
                  f"exceeds loaded sample buffer size {self.samples.size}. Truncating.")
            header.end = min(header.end, self.samples.size)
            if header.start >= header.end:
                print(f"Warning: Sample {sample_id} has invalid range after truncation. Skipping.")
                return None
        
        if header.start >= header.end:
            print(f"Warning: Sample {sample_id} ('{header.name}') has invalid range (start >= end). Skipping.")
            return None

        # Extract raw int16 data
        try:
            raw_data = self.samples[header.start:header.end]
            
            # Convert to float32 and normalize
            float_data = raw_data.astype(np.float32) / 32768.0
            
            # Handle looping if needed - this is often essential for correctly playing samples
            if (header.sample_type & 1) and header.loop_start < header.loop_end:
                # Validate loop points are within range
                loop_start = max(0, min(header.loop_start - header.start, len(float_data) - 1))
                loop_end = max(loop_start + 1, min(header.loop_end - header.start, len(float_data)))
                
                if loop_end > loop_start + 1:  # Valid loop found
                    # For now, we'll just ensure the sample ends at the loop end
                    # A full implementation would handle continuous looping during playback
                    if loop_end < len(float_data):
                        float_data = float_data[:loop_end]
            
            # Handle stereo samples
            # If this is part of a stereo pair, we'll return it as is and let the caller
            # handle combining with its pair if needed
            if header.sample_type & 0x2:  # Right channel
                # Flag it for stereo handling but return as is
                pass
            elif header.sample_type & 0x4:  # Left channel
                # Flag it for stereo handling but return as is
                pass
            elif header.sample_type & 0x8:  # Linked sample
                # This sample should be processed alongside its linked sample
                # For now, just return it as is
                pass
            
            # Apply pitch correction if needed (advanced implementation would do this during playback)
            pitch_correction = header.get_pitch_correction_semitones()
            if abs(pitch_correction) > 0.01:
                # Just note this - actual pitch shifting would happen during playback
                pass
            
            return float_data
            
        except Exception as e:
            print(f"Error extracting sample {sample_id} ('{header.name}'): {str(e)}")
            return None

    def parse(self):
        """Opens the file and parses the SF2 structure."""
        print(f"Opening SF2 file: {self.filepath}")
        try:
            with open(self.filepath, 'rb') as f:
                self.file = f
                self._parse_riff_header()

                # After finding all pdta chunks, load their data
                if self.chunks: # Check if any chunks were found
                    self._load_pdta_data()
                else:
                     print("Warning: No data chunks found in the file.")

                print("SF2 parsing complete.")
                # Load sample data while the file is still open
                self.load_sample_data()

        except FileNotFoundError:
            print(f"Error: File not found at {self.filepath}")
            raise
        except ValueError as e:
            print(f"Error parsing SF2 file: {e}")
            raise
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            raise
        finally:
            self.file = None # Ensure file handle is released

    def find_sample_for_note(self, preset_index: int, note: int, velocity: int = 64) -> Optional[Tuple[int, Dict]]:
        """
        Find the most appropriate sample for a given MIDI note in a preset.
        
        Args:
            preset_index: Index of the preset to use
            note: MIDI note number (0-127)
            velocity: MIDI velocity (0-127)
            
        Returns:
            Tuple of (sample_id, {dict of generators}) or None if no suitable sample found
        """
        try:
            if preset_index >= len(self.presets):
                print(f"Error: Invalid preset index {preset_index}")
                return None
                
            preset = self.presets[preset_index]
            
            # Step 1: Find matching preset zone
            matching_preset_zone = None
            preset_generators = {}
            global_preset_generators = {}
            
            # First check for global zone in preset (zone without instrument reference)
            for zone in preset.zones:
                if zone.instrument_id is None:
                    global_preset_generators = {gen.operator: gen.amount for gen in zone.generators}
                    break
            
            # Then find specific matching zone
            for zone in preset.zones:
                # Skip global zone (already processed)
                if zone.instrument_id is None:
                    continue
                    
                # Check if this zone's key and velocity ranges include our note
                if zone.key_range and (note < zone.key_range[0] or note > zone.key_range[1]):
                    continue
                    
                if zone.vel_range and (velocity < zone.vel_range[0] or velocity > zone.vel_range[1]):
                    continue
                    
                # Found a matching zone
                matching_preset_zone = zone
                preset_generators = {gen.operator: gen.amount for gen in zone.generators}
                break
            
            if not matching_preset_zone:
                return None
                
            # Step 2: Get the instrument from the matching preset zone
            instrument_id = matching_preset_zone.instrument_id
            if instrument_id is None or instrument_id >= len(self.instruments):
                print(f"Error: Invalid instrument ID {instrument_id} in preset {preset.name}")
                return None
                
            instrument = self.instruments[instrument_id]
            
            # Step 3: Find matching instrument zone
            instrument_zone = None
            instrument_generators = {}
            global_instrument_generators = {}
            
            # First check for global zone in instrument (zone without sample reference)
            for zone in instrument.zones:
                if zone.sample_id is None:
                    global_instrument_generators = {gen.operator: gen.amount for gen in zone.generators}
                    break
            
            # Then find specific matching zone
            for zone in instrument.zones:
                # Skip global zone (already processed)
                if zone.sample_id is None:
                    continue
                    
                # Check if this zone's key and velocity ranges include our note
                if zone.key_range and (note < zone.key_range[0] or note > zone.key_range[1]):
                    continue
                    
                if zone.vel_range and (velocity < zone.vel_range[0] or velocity > zone.vel_range[1]):
                    continue
                    
                # Found a matching zone
                instrument_zone = zone
                instrument_generators = {gen.operator: gen.amount for gen in zone.generators}
                break
                
            if not instrument_zone:
                return None
                
            # Step 4: Get the sample ID from the matching instrument zone
            sample_id = instrument_zone.sample_id
            if sample_id is None or sample_id >= len(self.sample_headers):
                print(f"Error: Invalid sample ID {sample_id} in instrument {instrument.name}")
                return None
            
            # Step 5: Combine generators in the correct order (global preset → local preset → global instrument → local instrument)
            all_generators = {}
            all_generators.update(global_preset_generators)
            all_generators.update(preset_generators)
            all_generators.update(global_instrument_generators)
            all_generators.update(instrument_generators)
                
            return (sample_id, all_generators)
        
        except Exception as e:
            print(f"Error finding sample for note {note}: {str(e)}")
            return None
    
    def get_stereo_sample(self, sample_id: int) -> Optional[np.ndarray]:
        """
        Get a proper stereo sample by finding and combining the left/right channels
        if the sample is part of a stereo pair.
        
        Args:
            sample_id: The ID of the sample to get
            
        Returns:
            A stereo sample with left and right channels properly arranged,
            or the original sample if not part of a stereo pair.
        """
        try:
            if sample_id >= len(self.sample_headers):
                return None
                
            header = self.sample_headers[sample_id]
            primary_sample = self.get_sample(sample_id)
            
            if primary_sample is None:
                return None
            
            # Convert mono sample to stereo
            if len(primary_sample.shape) == 1:
                primary_sample = np.column_stack((primary_sample, primary_sample))
                
            # Check if this is part of a stereo pair
            if (header.sample_type & 0x6) == 0:  # Not a left or right channel
                return primary_sample
                
            # Get the linked sample
            link_id = header.sample_link
            if link_id >= len(self.sample_headers) or link_id < 0:
                # Invalid link, return the primary sample
                print(f"Warning: Sample {sample_id} has invalid stereo link {link_id}")
                return primary_sample
                
            linked_sample = self.get_sample(link_id)
            if linked_sample is None:
                # Can't load linked sample, return the primary
                return primary_sample
                
            # Ensure linked sample is stereo too
            if len(linked_sample.shape) == 1:
                linked_sample = np.column_stack((linked_sample, linked_sample))
                
            # Determine which is left and which is right
            left_sample = None
            right_sample = None
            
            if header.sample_type & 0x4:  # This is the left channel
                left_sample = primary_sample
                right_sample = linked_sample
            elif header.sample_type & 0x2:  # This is the right channel
                left_sample = linked_sample
                right_sample = primary_sample
            else:
                # Not clearly a left or right, just return primary
                return primary_sample
                
            # Make sure they're the same length
            min_length = min(len(left_sample), len(right_sample))
            left_sample = left_sample[:min_length]
            right_sample = right_sample[:min_length]
            
            # Make sure we're using column vectors for the channels
            left_channel = left_sample[:, 0]  # Extract the first channel
            right_channel = right_sample[:, 0]  # Extract the first channel
            
            # Create the final stereo sample
            return np.column_stack((left_channel, right_channel))
            
        except Exception as e:
            print(f"Error creating stereo sample for sample {sample_id}: {str(e)}")
            return primary_sample if 'primary_sample' in locals() else None
    
    def modify_sample_for_playback(self, sample_data: np.ndarray, original_header, generators: Dict[int, int], note: int) -> np.ndarray:
        """
        Apply generator modifications to a sample for accurate playback.
        
        Args:
            sample_data: The raw sample data to modify
            original_header: The sample header for the original sample
            generators: Dictionary of generator types to values
            note: MIDI note number being played
            
        Returns:
            Modified sample data
        """
        try:
            if sample_data is None:
             return None

            # Ensure we have a 2D array (stereo)
            if len(sample_data.shape) == 1:
                sample_data = np.column_stack((sample_data, sample_data))
                
            # Apply various generator modifications
            
            # 1. Root key override (default to the original pitch if not specified)
            root_key = original_header.original_pitch
            if SF2Generator.OverridingRootKey in generators:
                root_key = generators[SF2Generator.OverridingRootKey]
                
            # 2. Fine tuning (in cents, -99 to +99)
            fine_tune = 0
            if hasattr(original_header, 'pitch_correction'):
                fine_tune = original_header.pitch_correction
                # Make sure the value is interpreted correctly as signed
                if fine_tune > 127:
                    fine_tune -= 256
                
            if SF2Generator.FineTune in generators:
                gen_fine_tune = generators[SF2Generator.FineTune]
                # Make sure the value is interpreted correctly as signed
                if gen_fine_tune > 32767:
                    gen_fine_tune -= 65536
                fine_tune += gen_fine_tune
                
            # 3. Coarse tuning (in semitones, -120 to +120)
            coarse_tune = 0
            if SF2Generator.CoarseTune in generators:
                coarse_tune = generators[SF2Generator.CoarseTune]
                # Make sure the value is interpreted correctly as signed
                if coarse_tune > 32767:
                    coarse_tune -= 65536
                
            # 4. Scale tuning (in cents per semitone, typically 100)
            scale_tuning = 100  # Default: 100 cents per semitone
            if SF2Generator.ScaleTuning in generators:
                scale_tuning = generators[SF2Generator.ScaleTuning]
                # Ensure it's not zero to avoid division issues
                if scale_tuning == 0:
                    scale_tuning = 1
                
            # Calculate pitch offset in semitones
            # The formula is:
            # semitones = (note - root_key) * (scale_tuning / 100.0) + coarse_tune + (fine_tune / 100.0)
            pitch_offset_semitones = 0.0
            
            # Calculate the key difference part
            key_diff = note - root_key
            key_diff_scaled = key_diff * (scale_tuning / 100.0)
            
            # Add all the components together
            pitch_offset_semitones = key_diff_scaled + coarse_tune + (fine_tune / 100.0)
            
            # Debug log to check values
            # print(f"Note: {note}, Root: {root_key}, Key diff: {key_diff}")
            # print(f"Scale: {scale_tuning}, Coarse: {coarse_tune}, Fine: {fine_tune}")
            # print(f"Total pitch offset: {pitch_offset_semitones} semitones")
            
            # Pitch shift the sample if needed
            if abs(pitch_offset_semitones) > 0.01:
                stretch_factor = 2 ** (pitch_offset_semitones / 12.0)
                
                original_length = len(sample_data)
                new_length = int(original_length / stretch_factor)
                
                # Ensure reasonable limits
                if new_length < 10:
                    return sample_data  # Too small to process
                
                # Debug log
                # print(f"Stretching sample by factor {stretch_factor} (new length: {new_length})")
                
                indices = np.linspace(0, original_length - 1, new_length)
                
                # Create output array with correct shape (ensure it's 2D for stereo)
                shifted_sample = np.zeros((new_length, 2), dtype=sample_data.dtype)
                
                # Process each channel
                for ch in range(min(2, sample_data.shape[1])):
                    shifted_sample[:, ch] = np.interp(indices, 
                                                np.arange(original_length), 
                                                sample_data[:, ch])
                
                return shifted_sample
            
            return sample_data
            
        except Exception as e:
            print(f"Error modifying sample for playback: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return sample_data

# --- Example Usage ---
if __name__ == "__main__":
    # Replace with the actual path to your SF2 file
    # sf2_path = "path/to/your/soundfont.sf2"
    sf2_path = input("Enter path to SF2 file: ")

    try:
        parser = SF2NewParser(sf2_path)
        parser.parse()

        print("\n--- SF2 Info ---")
        print(f"Version: {parser.info.version[0]}.{parser.info.version[1]}")
        print(f"Bank Name: {parser.info.bank_name}")
        print(f"Sound Engine: {parser.info.sound_engine}")
        print(f"Author: {parser.info.author}")
        print(f"Date: {parser.info.creation_date}")
        print(f"Tools: {parser.info.tools}")
        print(f"Comments: {parser.info.comments}")


        print(f"\n--- Found {len(parser.presets)} Presets ---")
        for i, preset in enumerate(parser.presets):
            print(f"  Preset {i}: Bank={preset.bank_num}, Num={preset.preset_num}, Name='{preset.name}', Zones={preset.num_zones}")
            # Optionally print zone details
            # for zi, zone in enumerate(preset.zones):
            #     print(f"    Zone {zi}: Key={zone.key_range}, Vel={zone.vel_range}, InstID={zone.instrument_id}")
            #     for gen in zone.generators:
            #         print(f"      Gen {gen.operator}: {gen.amount}")

        print(f"\n--- Found {len(parser.instruments)} Instruments ---")
        for i, inst in enumerate(parser.instruments):
            print(f"  Instrument {i}: Name='{inst.name}', Zones={inst.num_zones}")
            # Optionally print zone details
            # for zi, zone in enumerate(inst.zones):
            #     print(f"    Zone {zi}: Key={zone.key_range}, Vel={zone.vel_range}, SampleID={zone.sample_id}")
            #     for gen in zone.generators:
            #         print(f"      Gen {gen.operator}: {gen.amount}")


        print(f"\n--- Found {len(parser.sample_headers)} Sample Headers ---")
        for i, sh in enumerate(parser.sample_headers):
             print(f"  Sample {i}: Name='{sh.name}', Rate={sh.sample_rate}, Pitch={sh.original_pitch}, Corr={sh.get_pitch_correction_semitones():.2f} st, Type={sh.sample_type}, Link={sh.sample_link}, Range=[{sh.start}:{sh.end}], Loop=[{sh.loop_start}:{sh.loop_end}]")

        # --- Example: Load and access sample data ---
        # parser.load_sample_data()
        # if parser.samples is not None and len(parser.sample_headers) > 0:
        #     sample_id_to_get = 0 # Example: get the first sample
        #     sample_data = parser.get_sample(sample_id_to_get)
        #     if sample_data is not None:
        #         print(f"\n--- Sample {sample_id_to_get} Data ---")
        #         print(f"  Shape: {sample_data.shape}")
        #         print(f"  Min/Max: {np.min(sample_data):.4f} / {np.max(sample_data):.4f}")
        #         print(f"  Sample Rate (from header): {parser.sample_headers[sample_id_to_get].sample_rate}")


    except (FileNotFoundError, ValueError, Exception) as e:
        print(f"\nAn error occurred: {e}")