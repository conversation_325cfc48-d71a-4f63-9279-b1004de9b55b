import struct
from typing import List, Tu<PERSON>, Generator, Dict
import os
import numpy as np
import soundfile as sf
from concurrent.futures import <PERSON><PERSON>oolExecutor, ThreadPoolExecutor
from tqdm import tqdm
import multiprocessing
from scipy import signal
import imgui
import glfw
import OpenGL.GL as gl
from imgui.integrations.glfw import GlfwRenderer
import threading
from queue import Queue
import time
from tkinter import filedialog
import tkinter as tk
import sys
import json
import os.path
import random # Added
from numba import njit
from hexsf2newparser import SF2NewParser

# Hide tkinter root window
root = tk.Tk()
root.withdraw()

class MIDIEvent:
    __slots__ = ['time', 'event_type', 'channel', 'param1', 'param2', 'pitch_bend']
    def __init__(self, time, event_type, channel=None, param1=None, param2=None, pitch_bend=None):
        self.time = time
        self.event_type = event_type
        self.channel = channel
        self.param1 = param1
        self.param2 = param2
        self.pitch_bend = pitch_bend

class ProgressCallback:
    def __init__(self, message_queue):
        self.message_queue = message_queue
        self.last_update = 0

    def __call__(self, desc, current, total):
        current_time = time.time()
        if current_time - self.last_update >= 0.1:  # Update at most every 100ms
            percentage = (current / total) * 100 if total > 0 else 0
            self.message_queue.put(f"{desc}: {percentage:.1f}%")
            self.last_update = current_time

class MIDIRendererGUI:
    def __init__(self):
        # Default values
        self.midi_path = ""
        self.sf2_path = ""  # Changed from sample_folder
        self.polyphony_limit = 512
        self.release_time = 0.05
        self.chunk_duration = 65535.0
        self.processing_fps = 0  # Added
        self.fps_fluctuation = 0.0 # Added
        
        # Normalization settings
        self.target_peak = 0.08
        self.target_rms = -1.5
        self.attack = 100
        self.release = 1000
        self.max_boost_db = 0.08
        self.max_cut_db = -48

        # Sound Effects
        self.reverb_amount = 0.0
        self.chorus_amount = 0.0
        
        # Status information
        self.log_messages = []
        self.is_processing = False
        self.current_progress = 0
        self.message_queue = Queue()
        
        # QOL
        self.disable_release_fade = False
        
        # Load saved settings if they exist
        try:
            settings_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'hexsyn_settings.json')
            if os.path.exists(settings_path):
                with open(settings_path, 'r') as f:
                    settings = json.load(f)
                    
                self.midi_path = settings.get('midi_path', self.midi_path)
                self.sf2_path = settings.get('sf2_path', self.sf2_path)  # Changed from sample_folder
                self.polyphony_limit = settings.get('polyphony_limit', self.polyphony_limit)
                self.release_time = settings.get('release_time', self.release_time)
                self.chunk_duration = settings.get('chunk_duration', self.chunk_duration)
                self.target_peak = settings.get('target_peak', self.target_peak)
                self.target_rms = settings.get('target_rms', self.target_rms)
                self.attack = settings.get('attack', self.attack)
                self.release = settings.get('release', self.release)
                self.max_boost_db = settings.get('max_boost_db', self.max_boost_db)
                self.max_cut_db = settings.get('max_cut_db', self.max_cut_db)
                self.disable_release_fade = settings.get('disable_release_fade', self.disable_release_fade)
                self.processing_fps = settings.get('processing_fps', self.processing_fps) # Added
                self.fps_fluctuation = settings.get('fps_fluctuation', self.fps_fluctuation) # Added
                self.reverb_amount = settings.get('reverb_amount', self.reverb_amount) # Added
                self.chorus_amount = settings.get('chorus_amount', self.chorus_amount) # Added
        except Exception as e:
            print(f"Error loading settings: {str(e)}")
            
        # Initialize GLFW and ImGui
        self.init_imgui()

    def init_imgui(self):
        if not glfw.init():
            return False

        # Create window with default size
        monitor = glfw.get_primary_monitor()
        mode = glfw.get_video_mode(monitor)
        window_width = int(mode.size.width * 0.5)
        window_height = int(mode.size.height * 0.75)

        self.window = glfw.create_window(window_width, window_height, "HexSyn", None, None)
        if not self.window:
            glfw.terminate()
            return False

        glfw.make_context_current(self.window)
        imgui.create_context()
        self.impl = GlfwRenderer(self.window)
        
        # Set window position to center of screen
        window_pos_x = int((mode.size.width - window_width) / 2)
        window_pos_y = int((mode.size.height - window_height) / 2)
        glfw.set_window_pos(self.window, window_pos_x, window_pos_y)
        
        return True
        
    def auto_save_settings(self):
        settings = {
            'midi_path': self.midi_path,
            'sf2_path': self.sf2_path,  # Changed from sample_folder
            'polyphony_limit': self.polyphony_limit,
            'release_time': self.release_time,
            'chunk_duration': self.chunk_duration,
            'target_peak': self.target_peak,
            'target_rms': self.target_rms,
            'attack': self.attack,
            'release': self.release,
            'max_boost_db': self.max_boost_db,
            'max_cut_db': self.max_cut_db,
            'disable_release_fade': self.disable_release_fade,
            'processing_fps': self.processing_fps, # Added
            'fps_fluctuation': self.fps_fluctuation, # Added
            'reverb_amount': self.reverb_amount, # Added
            'chorus_amount': self.chorus_amount # Added
        }
        
        try:
            settings_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'hexsyn_settings.json')
            with open(settings_path, 'w') as f:
                json.dump(settings, f, indent=4)
        except Exception as e:
            pass  # Silently fail for auto-save to avoid spam

    def log(self, message):
        self.message_queue.put(message)

    def update_log(self):
        while not self.message_queue.empty():
            self.log_messages.append(self.message_queue.get())
            if len(self.log_messages) > 1000:  # Limit log size
                self.log_messages.pop(0)

    def select_midi_file(self):
        filename = filedialog.askopenfilename(
            title="Select MIDI File",
            filetypes=[("MIDI files", "*.mid"), ("All files", "*.*")]
        )
        if filename:
            self.midi_path = filename
            self.log(f"Selected MIDI file: {filename}")
            self.auto_save_settings()
    
    def select_sf2_file(self):  # Changed from select_sample_folder
        filename = filedialog.askopenfilename(
            title="Select SoundFont File",
            filetypes=[("SoundFont files", "*.sf2"), ("All files", "*.*")]
        )
        if filename:
            self.sf2_path = filename
            self.log(f"Selected SoundFont file: {filename}")
            self.auto_save_settings()

    def render_gui(self):
        # Set window size to 80% of the screen
        viewport = imgui.get_main_viewport()
        window_width = viewport.size.x * 1
        window_height = viewport.size.y * 1
        window_pos_x = (viewport.size.x - window_width) / 2
        window_pos_y = (viewport.size.y - window_height) / 2
    
        imgui.set_next_window_position(window_pos_x, window_pos_y)
        imgui.set_next_window_size(window_width, window_height)
    
        imgui.begin("HexSyn", True, 
                imgui.WINDOW_NO_RESIZE | 
                imgui.WINDOW_NO_MOVE)
    
        # File selection
        imgui.text("MIDI File:")
        imgui.same_line()
        if imgui.button("Open File##midi"):
            self.select_midi_file()
        imgui.text(self.midi_path)
    
        imgui.text("SoundFont File:")  # Changed from Sample Folder
        imgui.same_line()
        if imgui.button("Open SF2##soundfont"):  # Changed button label
            self.select_sf2_file()
        imgui.text(self.sf2_path)
    
        imgui.separator()
    
        # Settings
        changed, self.polyphony_limit = imgui.input_int("Polyphony Limit (per Track)", self.polyphony_limit)
        if changed:
            self.auto_save_settings()
    
        changed, self.release_time = imgui.input_float("Release Time (s)", self.release_time, format="%.3f")
        if changed:
            self.auto_save_settings()
    
        changed, self.chunk_duration = imgui.input_float("Chunk Duration (s)", self.chunk_duration, format="%.1f")
        if changed:
            self.auto_save_settings()
            
        changed, self.processing_fps = imgui.input_int("Processing FPS (0=disabled)", self.processing_fps) # Added
        if changed:
            self.auto_save_settings()
            
        changed, self.fps_fluctuation = imgui.input_float("FPS Fluctuation (0=disabled)", self.fps_fluctuation, format="%.3f") # Added
        if changed:
            self.auto_save_settings()
    
        imgui.separator()
        imgui.text("Audio Normalization Settings:")
        
        changed, self.target_peak = imgui.input_float("Target Peak (dB)", self.target_peak, format="%.3f")
        if changed:
            self.auto_save_settings()
    
        changed, self.target_rms = imgui.input_float("Target RMS (dB)", self.target_rms, format="%.3f")
        if changed:
            self.auto_save_settings()
    
        changed, self.attack = imgui.input_float("Attack", self.attack, format="%.1f")
        if changed:
            self.auto_save_settings()
    
        changed, self.release = imgui.input_float("Release", self.release, format="%.1f")
        if changed:
            self.auto_save_settings()
    
        changed, self.max_boost_db = imgui.input_float("Max Boost (dB)", self.max_boost_db, format="%.3f")
        if changed:
            self.auto_save_settings()
    
        changed, self.max_cut_db = imgui.input_float("Max Cut (dB)", self.max_cut_db, format="%.3f")
        if changed:
            self.auto_save_settings()
    
        imgui.separator()
        imgui.text("Sample Handling Settings:")
        
        changed, self.disable_release_fade = imgui.checkbox(
            "Disable fading out samples (Can cause clicks when killing the samples, can also improve rendering speed)", 
            self.disable_release_fade
        )
        if changed:
            self.auto_save_settings()

        imgui.separator()
        imgui.text("Sound Effects:")

        changed, self.reverb_amount = imgui.slider_float("Reverb (%)", self.reverb_amount, 0.0, 100.0, "%.1f")
        if changed:
            self.auto_save_settings()

        changed, self.chorus_amount = imgui.slider_float("Chorus (%)", self.chorus_amount, 0.0, 100.0, "%.1f")
        if changed:
            self.auto_save_settings()
    
        imgui.separator()
    
        # Process button
        if not self.is_processing:
            if imgui.button("Process MIDI", width=120, height=30):
                if not self.midi_path or not self.sf2_path:  # Changed to sf2_path
                    self.log("Please select both MIDI file and sample folder")
                else:
                    self.start_processing()
        else:
            imgui.text("Processing... Please wait")
    
        # Log window
        imgui.begin_child("Log Window", 0, 0, border=True)
        imgui.push_text_wrap_pos(0)
        for message in self.log_messages:
            imgui.text(message)
        if self.log_messages:
            imgui.set_scroll_here_y(1.0)  # Auto-scroll to bottom
        imgui.pop_text_wrap_pos()
        imgui.end_child()
    
        imgui.end()
        
        self.update_log()
        # Continuing from the previous code...
        
    def start_processing(self):
        if not os.path.exists(self.midi_path) or not os.path.exists(self.sf2_path):  # Changed condition
            self.log("Please select valid MIDI and SoundFont files")
            return
    
        self.is_processing = True
        self.log_messages.clear()
        
        def process_thread():
            try:
                self.log(f"Loading SoundFont file: {self.sf2_path}")
                
                # Create a progress callback that routes to our log
                def sf_progress_callback(desc, current, total):
                    if isinstance(desc, str) and desc.startswith("Warning:"):
                        # Don't flood the UI with every warning
                        if current % 10 == 0:  # Only show every 10th warning
                            self.log(desc)
                    elif isinstance(current, int) and isinstance(total, int) and total > 0:
                        percentage = (current / total) * 100
                        if current % (max(1, total // 20)) == 0 or current == total:  # ~20 updates
                            self.log(f"{desc}: {percentage:.1f}%")
                    else:
                        self.log(desc)
                
                # Use the improved load_samples function that handles proper instrument mapping
                samples = load_samples(
                    self.sf2_path,
                    progress_callback=sf_progress_callback,
                    disable_release_fade=self.disable_release_fade,
                    release_time=self.release,
                    target_sample_rate=44100  # Use standard sample rate for pre-processing
                )
                
                if not samples:
                    self.log("No usable samples found in the SoundFont. Please try a different file.")
                    return
                
                self.log(f"Successfully loaded {len(samples)} usable samples for MIDI notes")
                
                # Show the note range available
                note_range = list(samples.keys())
                if note_range:
                    min_note = min(note_range)
                    max_note = max(note_range)
                    self.log(f"Note range: {min_note} to {max_note} ({len(note_range)} notes)")
                
                output_path = os.path.splitext(self.midi_path)[0] + "_rendered.mp3"
                self.log(f"Beginning MIDI rendering to {output_path}")
                
                render_midi_to_audio(
                    self.midi_path,
                    samples,
                    output_path,
                    self.chunk_duration,
                    self.polyphony_limit,
                    self.release_time,
                    self.target_peak,
                    self.target_rms,
                    self.attack,
                    self.release,
                    self.max_boost_db,
                    self.max_cut_db,
                    self.message_queue,
                    self.disable_release_fade,
                    self.processing_fps, # Added
                    self.fps_fluctuation, # Added
                    self.reverb_amount, # Added
                    self.chorus_amount # Added
                )
                
            except Exception as e:
                self.log(f"Error: {str(e)}")
                import traceback
                self.log(traceback.format_exc())
            finally:
                self.is_processing = False
        
        threading.Thread(target=process_thread, daemon=True).start()

    def run(self):
        while not glfw.window_should_close(self.window):
            glfw.poll_events()
            self.impl.process_inputs()
            
            # Clear frame
            gl.glClearColor(0.1, 0.1, 0.1, 1)
            gl.glClear(gl.GL_COLOR_BUFFER_BIT)
            
            # Start new frame
            imgui.new_frame()
            self.render_gui()
            
            # End frame
            imgui.end_frame()
            
            # Render
            imgui.render()
            self.impl.render(imgui.get_draw_data())
            
            # Swap buffers
            glfw.swap_buffers(self.window)
        
        self.impl.shutdown()
        glfw.terminate()

def parse_variable_length(data: bytes, offset: int) -> Tuple[int, int]:
    value = 0
    while True:
        byte = data[offset]
        value = (value << 7) | (byte & 0x7F)
        offset += 1
        if not (byte & 0x80):
            break
    return value, offset

def parse_midi_events(data: bytes) -> Generator[MIDIEvent, None, None]:
    offset = 0
    time = 0
    running_status = None

    # RPN state tracking and pitch bend range (from midi_loader.py)
    rpn_state = {}
    pitch_bend_range = {}
    channel_pitch_bend = {}  # Track current pitch bend per channel (in semitones)

    for ch in range(16):
        rpn_state[ch] = {'RPN_MSB': None, 'RPN_LSB': None, 'fine': 0}
        pitch_bend_range[ch] = 2.0  # Default pitch bend range is 2 semitones
        channel_pitch_bend[ch] = 0.0  # Current pitch bend in semitones
    
    while offset < len(data):
        delta_time, offset = parse_variable_length(data, offset)
        time += delta_time
        
        if data[offset] < 0x80:  # Running status
            if running_status is None:
                raise ValueError("Running status without previous status byte")
            status = running_status
        else:
            status = data[offset]
            offset += 1
            running_status = status

        if status == 0xFF:  # Meta event
            meta_type = data[offset]
            offset += 1
            length, offset = parse_variable_length(data, offset)
            if meta_type == 0x51:  # Set tempo
                tempo = struct.unpack('>I', b'\x00' + data[offset:offset+3])[0]
                yield MIDIEvent(time, 'set_tempo', param1=tempo)
            elif meta_type == 0x2F:  # End of track
                yield MIDIEvent(time, 'end_of_track')
                break
            offset += length
        elif status == 0xF0 or status == 0xF7:  # SysEx event
            length, offset = parse_variable_length(data, offset)
            offset += length
        else:
            channel = status & 0x0F
            event_type = status & 0xF0
            
            if event_type in [0x80, 0x90, 0xA0, 0xB0, 0xE0]:
                if event_type == 0xB0:  # Control Change (CC events)
                    param1, param2 = data[offset], data[offset+1]
                    offset += 2
                    cc_number, cc_value = param1, param2

                    # Handle RPN (Registered Parameter Number) events for pitch bend range
                    if cc_number == 101:  # RPN MSB
                        rpn_state[channel]['RPN_MSB'] = cc_value
                    elif cc_number == 100:  # RPN LSB
                        rpn_state[channel]['RPN_LSB'] = cc_value
                    elif cc_number == 6:  # Data Entry MSB
                        if rpn_state[channel]['RPN_MSB'] == 0 and rpn_state[channel]['RPN_LSB'] == 0:
                            pitch_bend_range[channel] = float(cc_value)
                    elif cc_number == 38:  # Data Entry LSB (fine tuning)
                        if rpn_state[channel]['RPN_MSB'] == 0 and rpn_state[channel]['RPN_LSB'] == 0:
                            rpn_state[channel]['fine'] = cc_value
                            pitch_bend_range[channel] = float(pitch_bend_range[channel]) + (cc_value / 100.0)
                    elif cc_number in (101, 100) and cc_value == 127:  # Reset RPN
                        rpn_state[channel]['RPN_MSB'] = None
                        rpn_state[channel]['RPN_LSB'] = None

                elif event_type == 0xE0:  # Pitch bend
                    # Pitch bend is a 14-bit value, centered at 8192 (no bend)
                    lsb, msb = data[offset], data[offset+1]
                    offset += 2
                    bend_value = (msb << 7) + lsb
                    range_semitones = pitch_bend_range.get(channel, 2.0)
                    semitone_offset = ((bend_value - 8192) / 8192) * range_semitones
                    channel_pitch_bend[channel] = semitone_offset

                    yield MIDIEvent(time, 'pitch_bend', channel,
                                    pitch_bend=semitone_offset)
                else:
                    param1, param2 = data[offset], data[offset+1]
                    offset += 2

                    if event_type == 0x90 and param2 > 0:  # Note on
                        yield MIDIEvent(time, 'note_on', channel, param1, param2,
                                        pitch_bend=channel_pitch_bend[channel])
                    elif event_type == 0x80 or (event_type == 0x90 and param2 == 0):  # Note off
                        yield MIDIEvent(time, 'note_off', channel, param1, param2,
                                        pitch_bend=channel_pitch_bend[channel])
            
            elif event_type in [0xC0, 0xD0]:
                param1 = data[offset]
                offset += 1


def parse_midi_file(filename: str, progress_callback=None) -> Tuple[int, int, List[bytes], List[Tuple[int, int]]]:
    with open(filename, 'rb') as file:
        if file.read(4) != b'MThd':
            raise ValueError("Not a valid MIDI file")
        
        header_length = struct.unpack('>I', file.read(4))[0]
        format_type, num_tracks, ticks_per_quarter_note = struct.unpack('>HHH', file.read(6))
        
        tracks = []
        all_tempo_changes = []
        
        for i in range(num_tracks):
            if progress_callback:
                progress_callback("Loading MIDI tracks", i, num_tracks)
                
            if file.read(4) != b'MTrk':
                raise ValueError(f"Track {i} is not valid")
            
            track_length = struct.unpack('>I', file.read(4))[0]
            track_data = file.read(track_length)
            tracks.append(track_data)
            
            # Extract tempo changes from each track
            track_tempo_changes = []
            for event in parse_midi_events(track_data):
                if event.event_type == 'set_tempo':
                    track_tempo_changes.append((event.time, event.param1))
            all_tempo_changes.extend(track_tempo_changes)

        # Sort and merge all tempo changes
        all_tempo_changes.sort(key=lambda x: x[0])
        merged_tempo_changes = []
        for time, tempo in all_tempo_changes:
            if not merged_tempo_changes or merged_tempo_changes[-1][1] != tempo:
                merged_tempo_changes.append((time, tempo))

        if not merged_tempo_changes:
            merged_tempo_changes = [(0, 500000)]  # Default tempo at the start (120 BPM)

        return ticks_per_quarter_note, num_tracks, tracks, merged_tempo_changes

def load_samples(sf2_path: str, progress_callback=None, disable_release_fade: bool = False,
                release_time: float = 0.1, target_sample_rate: int = 44100) -> Dict[int, Tuple[np.ndarray, int]]:
    """
    Load samples from a SoundFont file using the SF2NewParser.
    Uses the improved sample loading logic to handle stereo samples and proper pitch mapping.

    Args:
        sf2_path: Path to the SoundFont (.sf2) file
        progress_callback: Optional callback function for progress reporting
        disable_release_fade: If False, pre-process samples with fade-out for optimization
        release_time: Release time in seconds for fade-out pre-processing
        target_sample_rate: Target sample rate for fade-out calculations

    Returns:
        Dictionary mapping MIDI note numbers to (sample_data, sample_rate) tuples
    """
    try:
        if progress_callback:
            progress_callback("Initializing SF2 parser", 0, 100)
        
        parser = SF2NewParser(sf2_path)
        
        if progress_callback:
            progress_callback("Parsing SF2 structure", 10, 100)
        
        parser.parse()
        
        if progress_callback:
            progress_callback(f"Found {len(parser.sample_headers)} samples", 20, 100)
            progress_callback(f"Found {len(parser.presets)} presets", 25, 100)
            progress_callback(f"Found {len(parser.instruments)} instruments", 30, 100)
        
        # Build the samples dictionary
        # The key is the MIDI note number
        samples = {}
        
        # First, extract all unique sample IDs from the first preset
        preset_index = 0
        preset_mapping_success = False
        
        if len(parser.presets) > 0:
            # Try to use the first GM piano preset if available (bank 0, program 0)
            piano_preset_idx = None
            for i, preset in enumerate(parser.presets):
                if preset.bank_num == 0 and preset.preset_num == 0:
                    piano_preset_idx = i
                    break
            
            if piano_preset_idx is not None:
                preset_index = piano_preset_idx
                preset_name = parser.presets[preset_index].name
                if progress_callback:
                    progress_callback(f"Using piano preset: {preset_name}", 35, 100)
            else:
                preset_name = parser.presets[preset_index].name
                if progress_callback:
                    progress_callback(f"Using first preset: {preset_name}", 35, 100)
            
            # We'll map the full MIDI note range (0-127)
            sample_map = {}
            successful_notes = 0
            
            for note in range(128):
                if note % 12 == 0 and progress_callback:
                    progress_percentage = 40 + (note * 30 // 128)
                    progress_callback(f"Mapping MIDI note {note}", progress_percentage, 100)
                
                try:
                    result = parser.find_sample_for_note(preset_index, note, 64)
                    if result:
                        sample_id, generators = result
                        sample_map[note] = (sample_id, generators)
                        successful_notes += 1
                except Exception as e:
                    if progress_callback and note % 12 == 0:
                        progress_callback(f"Warning: Failed to map note {note}: {str(e)}", 
                                 40 + (note * 30 // 128), 100)
                    continue
            
            if progress_callback:
                progress_callback(f"Successfully mapped {successful_notes} notes", 70, 100)
            
            # If we found at least some mappings, consider it a success
            preset_mapping_success = successful_notes > 0
            
            # Now load the actual samples and apply appropriate modifications
            if preset_mapping_success:
                processed_samples = 0
                total_samples = len(sample_map)
                
                for note, (sample_id, generators) in sample_map.items():
                    if progress_callback and processed_samples % max(1, total_samples // 10) == 0:
                        progress_percentage = 70 + (processed_samples * 25 // total_samples)
                        progress_callback(f"Processing sample {processed_samples+1}/{total_samples}", 
                                         progress_percentage, 100)
                    
                    try:
                        # Try to get the sample as a stereo pair if possible
                        sample_data = parser.get_stereo_sample(sample_id)
                        
                        if sample_data is not None:
                            # Safety check: ensure sample is 2D (stereo)
                            if len(sample_data.shape) == 1:
                                sample_data = np.column_stack((sample_data, sample_data))
                            
                            # Get the sample header for pitch information
                            header = parser.sample_headers[sample_id]
                            
                            # Modify the sample for playback (pitch shifting, etc.)
                            modified_sample = parser.modify_sample_for_playback(sample_data, header, generators, note)
                            
                            if modified_sample is not None:
                                # Ensure the modified sample is still stereo
                                if len(modified_sample.shape) == 1:
                                    modified_sample = np.column_stack((modified_sample, modified_sample))
                                
                                # Store sample with its sample rate
                                samples[note] = (modified_sample, header.sample_rate)
                        
                        processed_samples += 1
                        
                    except Exception as e:
                        if progress_callback and processed_samples % 10 == 0:
                            progress_callback(f"Warning: Failed to process note {note}: {str(e)}", 
                                             70 + (processed_samples * 25 // total_samples), 100)
                        continue
        
        # If preset mapping was unsuccessful or no presets were found, fall back to direct sample loading
        if not preset_mapping_success:
            if progress_callback:
                progress_callback("Preset mapping failed, falling back to direct sample loading", 40, 100)
            
            sample_count = len(parser.sample_headers)
            for i, sample_header in enumerate(parser.sample_headers):
                if progress_callback and i % max(1, sample_count // 20) == 0:
                    progress_percentage = 40 + (i * 55 // sample_count)
                    progress_callback(f"Processing sample {i+1}/{sample_count}", progress_percentage, 100)
                
                try:
                    # Try to get the sample as a stereo pair if possible
                    sample_data = parser.get_stereo_sample(i)
                    
                    if sample_data is not None:
                        # Ensure sample is stereo
                        if len(sample_data.shape) == 1:
                            sample_data = np.column_stack((sample_data, sample_data))
                        
                        # Store sample with its sample rate, using the original pitch as key
                        note = sample_header.original_pitch
                        if 0 <= note <= 127:  # Verify note is in valid MIDI range
                            samples[note] = (sample_data, sample_header.sample_rate)
                        
                except Exception as e:
                    if progress_callback and i % 10 == 0:
                        progress_callback(f"Warning: Failed to process sample {i} ({sample_header.name}): {str(e)}", 
                                         40 + (i * 55 // sample_count), 100)
                    continue
        
        # Final check: ensure we have at least some samples
        if not samples:
            # Last resort: just load samples directly without any mapping
            if progress_callback:
                progress_callback("All previous methods failed, loading raw samples", 90, 100)
            
            for i, sample_header in enumerate(parser.sample_headers):
                try:
                    raw_sample = parser.get_sample(i)
                    if raw_sample is not None:
                        if len(raw_sample.shape) == 1:
                            raw_sample = np.column_stack((raw_sample, raw_sample))
                        
                        note = sample_header.original_pitch
                        if 0 <= note <= 127:
                            samples[note] = (raw_sample, sample_header.sample_rate)
                except Exception:
                    continue
        
        # Pre-process samples with fade-out if enabled (when disable_release_fade is False)
        if not disable_release_fade and samples:
            if progress_callback:
                progress_callback("Pre-processing samples with fade-out...", 95, 100)

            processed_samples = {}
            for note, (sample_data, sample_rate) in samples.items():
                # Apply fade-out to the end of each sample
                processed_sample = apply_fadeout_to_sample(sample_data, release_time, sample_rate)
                processed_samples[note] = (processed_sample, sample_rate)

            samples = processed_samples

        if progress_callback:
            if samples:
                # Show the note range we managed to load
                min_note = min(samples.keys()) if samples else 0
                max_note = max(samples.keys()) if samples else 0
                fade_status = "with pre-processed fade-out" if not disable_release_fade else "without fade-out pre-processing"
                progress_callback(f"Successfully loaded {len(samples)} usable samples (range: {min_note}-{max_note}) {fade_status}", 100, 100)
            else:
                progress_callback("Failed to load any usable samples from this SoundFont", 100, 100)

        return samples
        
    except Exception as e:
        if progress_callback:
            progress_callback(f"Error loading SF2 file: {str(e)}", 100, 100)
        import traceback
        if progress_callback:
            progress_callback(traceback.format_exc(), 100, 100)
        print(traceback.format_exc())  # Print stack trace to console as well
        return {}

def tick2second(tick: int, ticks_per_beat: int, tempo: int) -> float:
    return (tick * tempo) / (ticks_per_beat * 1000000)

def scale_velocity(velocity: int) -> float:
    velocity = max(0, min(127, velocity))
    scaled = np.log(velocity + 1) / np.log(128)
    curved = np.power(scaled, 16)
    normalized = (curved - 0) / (1 - 0)
    return normalized

# Cache for pre-computed release envelopes to avoid repeated computation
_release_envelope_cache = {}
_max_cache_size = 1000

# Pre-computed lookup table for common envelope sizes (power of 2 sizes up to 64k samples)
_common_envelope_sizes = [64, 128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768, 65536]
_precomputed_envelopes = {}

@njit(fastmath=True, cache=True)
def _create_release_envelope_numba(length: int, release_samples: int) -> np.ndarray:
    """Numba-optimized release envelope creation"""
    if length <= release_samples:
        # Create linear fade from 1.0 to 0.0 for entire length
        envelope = np.empty(length, dtype=np.float32)
        for i in range(length):
            envelope[i] = 1.0 - (i / (length - 1)) if length > 1 else 0.0
        return envelope
    else:
        # Create envelope with ones, then linear fade at the end
        envelope = np.ones(length, dtype=np.float32)
        fade_start = length - release_samples
        for i in range(release_samples):
            envelope[fade_start + i] = 1.0 - (i / (release_samples - 1)) if release_samples > 1 else 0.0
        return envelope

def _initialize_precomputed_envelopes(release_time: float, sample_rate: int):
    """Initialize pre-computed envelopes for common sizes"""
    global _precomputed_envelopes
    release_samples = int(release_time * sample_rate)

    for size in _common_envelope_sizes:
        if size not in _precomputed_envelopes:
            _precomputed_envelopes[size] = {}
        if release_samples not in _precomputed_envelopes[size]:
            _precomputed_envelopes[size][release_samples] = _create_release_envelope_numba(size, release_samples)

def create_release_envelope(length: int, release_time: float, sample_rate: int) -> np.ndarray:
    """Creates a linear release envelope with caching and pre-computed lookup for performance"""
    release_samples = int(release_time * sample_rate)

    # Check pre-computed envelopes first for common sizes
    if length in _precomputed_envelopes and release_samples in _precomputed_envelopes[length]:
        return _precomputed_envelopes[length][release_samples].copy()

    # Create cache key for less common sizes
    cache_key = (length, release_samples)

    # Check cache
    if cache_key in _release_envelope_cache:
        return _release_envelope_cache[cache_key].copy()

    # Clear cache if it gets too large
    if len(_release_envelope_cache) >= _max_cache_size:
        # Keep only the most recently used half
        keys_to_remove = list(_release_envelope_cache.keys())[:-_max_cache_size//2]
        for key in keys_to_remove:
            del _release_envelope_cache[key]

    # Create new envelope using Numba-optimized function
    envelope = _create_release_envelope_numba(length, release_samples)

    # Cache the result for non-common sizes
    if length not in _common_envelope_sizes:
        _release_envelope_cache[cache_key] = envelope.copy()

    return envelope

@njit(fastmath=True, cache=True)
def _apply_release_envelope_numba(audio_segment: np.ndarray, envelope: np.ndarray, velocity: float) -> np.ndarray:
    """Numba-optimized envelope application for both mono and stereo"""
    if audio_segment.ndim == 1:
        # Mono case
        result = np.empty_like(audio_segment)
        for i in range(len(audio_segment)):
            result[i] = audio_segment[i] * envelope[i] * velocity
        return result
    else:
        # Stereo case
        result = np.empty_like(audio_segment)
        for i in range(len(audio_segment)):
            for ch in range(audio_segment.shape[1]):
                result[i, ch] = audio_segment[i, ch] * envelope[i] * velocity
        return result

def apply_release_envelope_optimized(audio_segment: np.ndarray, segment_length: int,
                                   release_time: float, sample_rate: int, velocity: float) -> np.ndarray:
    """Optimized release envelope application with caching and Numba acceleration"""
    if segment_length <= 0:
        return np.zeros_like(audio_segment[:0])

    # Get cached envelope
    envelope = create_release_envelope(segment_length, release_time, sample_rate)

    # Apply envelope using Numba-optimized function
    return _apply_release_envelope_numba(audio_segment[:segment_length], envelope, velocity)

def apply_fadeout_to_sample(sample_data: np.ndarray, release_time: float, sample_rate: int) -> np.ndarray:
    """
    Pre-process a sample by applying fade-out to the end.
    This is done once during sample loading instead of during rendering for optimization.
    """
    if len(sample_data) == 0:
        return sample_data

    # Calculate fade-out length in samples
    fade_samples = int(release_time * sample_rate)

    # If the sample is shorter than the fade time, fade the entire sample
    if len(sample_data) <= fade_samples:
        fade_envelope = np.linspace(1.0, 0.0, len(sample_data))
    else:
        # Create fade envelope that only affects the end of the sample
        fade_envelope = np.ones(len(sample_data))
        fade_envelope[-fade_samples:] = np.linspace(1.0, 0.0, fade_samples)

    # Apply fade envelope to all channels
    if sample_data.ndim == 1:
        # Mono sample
        return sample_data * fade_envelope
    else:
        # Stereo sample - apply fade to both channels
        fade_envelope = fade_envelope.reshape(-1, 1)
        return sample_data * fade_envelope

def preprocess_sample_with_fadeout(sample_data: np.ndarray, release_time: float, sample_rate: int) -> np.ndarray:
    """
    Pre-process a sample by applying fade-out at the end for optimization.
    This eliminates the need to apply fade envelopes during rendering.
    Uses optimized vectorized operations for maximum speed.

    Args:
        sample_data: The sample data to process
        release_time: Release time in seconds
        sample_rate: Sample rate for calculating fade length

    Returns:
        Sample data with fade-out pre-applied
    """
    if len(sample_data) == 0:
        return sample_data

    # Calculate fade-out length in samples
    fade_samples = int(release_time * sample_rate)

    # Early return if no fade needed
    if fade_samples <= 0:
        return sample_data

    # Create a copy to avoid modifying the original
    result = sample_data.copy()

    # If the sample is shorter than the fade time, fade the entire sample
    if len(sample_data) <= fade_samples:
        # Use optimized linspace for the entire sample
        fade_envelope = np.linspace(1.0, 0.0, len(sample_data), dtype=np.float32)
        if sample_data.ndim == 1:
            result *= fade_envelope
        else:
            # Broadcasting for multi-channel
            result *= fade_envelope[:, np.newaxis]
    else:
        # Apply fade-out only to the end portion - more memory efficient
        fade_start = len(sample_data) - fade_samples
        fade_envelope = np.linspace(1.0, 0.0, fade_samples, dtype=np.float32)

        if sample_data.ndim == 1:
            result[fade_start:] *= fade_envelope
        else:
            # Broadcasting for multi-channel - more efficient than reshape
            result[fade_start:] *= fade_envelope[:, np.newaxis]

    return result

def quantize_time(time_sec: float, processing_fps: int, fps_fluctuation: float) -> float:
    """Quantizes a time value based on FPS and fluctuation."""
    if processing_fps <= 0:
        return time_sec
    
    factor = 1.0
    if fps_fluctuation > 0:
        # Ensure fluctuation doesn't result in zero or negative FPS
        min_factor = 1.0 / (1.0 + fps_fluctuation) if fps_fluctuation < 1 else 0.01
        max_factor = 1.0 + fps_fluctuation
        factor = random.uniform(min_factor, max_factor)
        
    effective_fps = processing_fps * factor
    if effective_fps <= 0: # Avoid division by zero
        return time_sec
        
    dt = 1.0 / effective_fps
    return round(time_sec / dt) * dt
    
def process_track_chunk(track_data: bytes, samples: Dict[int, Tuple[np.ndarray, int]],
                       ticks_per_beat: int, tempo_changes: List[Tuple[int, int]],
                       sample_rate: int, chunk_start_time: float, chunk_end_time: float,
                       polyphony_limit: int, release_time: float,
                       processing_fps: int, fps_fluctuation: float, # Added
                       disable_release_fade: bool = False) -> np.ndarray:
    events = list(parse_midi_events(track_data))
    
    if not events:
        return np.zeros((int((chunk_end_time - chunk_start_time) * sample_rate), 2), dtype=np.float32)

    chunk_duration = chunk_end_time - chunk_start_time
    output = np.zeros((int(chunk_duration * sample_rate), 2), dtype=np.float32)

    active_notes = {}
    next_note_id = 0
    note_ids = {}  # Maps (channel, note) to list of active note IDs
    channel_notes_count = {i: 0 for i in range(16)}  # Track active notes per channel
    channel_pitch_bend = {i: 0.0 for i in range(16)}
    current_tempo_index = 0
    current_time = 0

    def get_current_time(event_tick):
        nonlocal current_tempo_index, current_time
        while current_tempo_index < len(tempo_changes) - 1 and event_tick >= tempo_changes[current_tempo_index + 1][0]:
            next_tempo_tick, next_tempo = tempo_changes[current_tempo_index + 1]
            current_time += tick2second(next_tempo_tick - tempo_changes[current_tempo_index][0], 
                                     ticks_per_beat, tempo_changes[current_tempo_index][1])
            current_tempo_index += 1
        
        remaining_ticks = event_tick - tempo_changes[current_tempo_index][0]
        return current_time + tick2second(remaining_ticks, ticks_per_beat, tempo_changes[current_tempo_index][1])

    def resample_segment(segment: np.ndarray, output_length: int) -> np.ndarray:
        """Resample a segment to a different length using linear interpolation (from midi_loader.py)"""
        if len(segment) == 0:
            return np.zeros((output_length, segment.shape[1]), dtype=segment.dtype)
        x_old = np.linspace(0, 1, num=len(segment))
        x_new = np.linspace(0, 1, num=output_length)
        resampled = []
        for ch in range(segment.shape[1]):
            resampled_channel = np.interp(x_new, x_old, segment[:, ch])
            resampled.append(resampled_channel)
        return np.stack(resampled, axis=-1).astype(segment.dtype)

    def apply_pitch_bend_to_segment(original_sample: np.ndarray, input_offset: int,
                                   output_samples: int, semitone_offset: float) -> np.ndarray:
        """Apply pitch bending using proper resampling (from midi_loader.py approach)"""
        if abs(semitone_offset) < 0.01:
            # No pitch bend, just take the segment directly
            end_offset = min(input_offset + output_samples, len(original_sample))
            segment = original_sample[input_offset:end_offset]
            if len(segment) < output_samples:
                # Pad with zeros if we don't have enough samples
                pad_width = output_samples - len(segment)
                segment = np.concatenate([segment, np.zeros((pad_width, segment.shape[1]), dtype=segment.dtype)], axis=0)
            return segment

        # Calculate pitch factor and required input samples
        pitch_factor = 2 ** (semitone_offset / 12.0)
        input_samples_needed = int(output_samples * pitch_factor)

        # Extract the input segment
        end_offset = min(input_offset + input_samples_needed, len(original_sample))
        input_segment = original_sample[input_offset:end_offset]

        # Pad with zeros if we don't have enough input samples
        if len(input_segment) < input_samples_needed:
            pad_width = input_samples_needed - len(input_segment)
            input_segment = np.concatenate([input_segment, np.zeros((pad_width, input_segment.shape[1]), dtype=input_segment.dtype)], axis=0)

        # Resample to the desired output length
        return resample_segment(input_segment, output_samples)

    def handle_note_stealing(channel: int, velocity: float):
        if channel_notes_count[channel] < polyphony_limit:
            return None
            
        channel_notes = [(id, info) for id, info in active_notes.items() 
                        if info['channel'] == channel]
        
        if not channel_notes:
            return None
            
        # Try to steal releasing notes first
        releasing_notes = [(id, info) for id, info in channel_notes 
                         if info.get('is_releasing', False)]
        if releasing_notes:
            return min(releasing_notes, key=lambda x: x[1]['velocity'])[0]
            
        # Then try to steal quieter notes
        quiet_notes = [(id, info) for id, info in channel_notes 
                      if info['velocity'] < velocity]
        if quiet_notes:
            return min(quiet_notes, key=lambda x: x[1]['velocity'])[0]
            
        # Finally steal the oldest note
        return min(channel_notes, key=lambda x: x[1]['start_time'])[0]

    for event in events:
        event_time_raw = get_current_time(event.time)
        
        # Apply quantization if enabled
        event_time = quantize_time(event_time_raw, processing_fps, fps_fluctuation)

        if event_time < chunk_start_time:
            # Still need to process pitch bends that might affect notes starting later
            if event.event_type == 'pitch_bend':
                 channel_pitch_bend[event.channel] = event.pitch_bend
            continue # Skip rendering for events before chunk start
        if event_time >= chunk_end_time:
            break

        if event.event_type == 'pitch_bend':
            new_pitch_bend = event.pitch_bend
            
            for note_id, note_info in list(active_notes.items()):
                if note_info['channel'] == event.channel:
                    segment_start = note_info['segment_start']
                    segment_end = int((event_time - chunk_start_time) * sample_rate)
                    
                    if segment_end > segment_start:
                        output_samples = segment_end - segment_start

                        # Apply pitch bending using proper resampling
                        pitched_segment = apply_pitch_bend_to_segment(
                            note_info['original_sample'],
                            note_info['input_offset'],
                            output_samples,
                            note_info['current_pitch_bend']
                        )

                        # Calculate how much of the original sample was consumed
                        if abs(note_info['current_pitch_bend']) < 0.01:
                            input_consumed = output_samples
                        else:
                            pitch_factor = 2 ** (note_info['current_pitch_bend'] / 12.0)
                            input_consumed = int(output_samples * pitch_factor)

                        actual_end = min(segment_end, len(output))
                        segment_length = min(len(pitched_segment), actual_end - segment_start)
                        if segment_length > 0:
                            output[segment_start:segment_start + segment_length] += \
                                pitched_segment[:segment_length] * note_info['velocity']

                    active_notes[note_id].update({
                        'segment_start': segment_end,
                        'input_offset': note_info['input_offset'] + input_consumed,
                        'current_pitch_bend': new_pitch_bend
                    })
            
            channel_pitch_bend[event.channel] = new_pitch_bend

        elif event.event_type == 'note_on':
            note_start = int((event_time - chunk_start_time) * sample_rate)
            note = event.param1
            velocity = scale_velocity(event.param2)
            
            if velocity > 0 and note in samples:
                # Handle note stealing if needed
                note_to_steal = handle_note_stealing(event.channel, velocity)
                if note_to_steal is not None:
                    stolen_note = active_notes[note_to_steal]
                    if not disable_release_fade:
                        # Apply quick fade-out when stealing notes (like older version)
                        quick_release = min(0.01, release_time / 2)
                        segment_start = stolen_note['segment_start']
                        note_end = int((event_time - chunk_start_time + quick_release) * sample_rate)

                        if note_end > segment_start:
                            output_samples = note_end - segment_start

                            # Apply pitch bending and quick fade-out for stolen note
                            pitched_segment = apply_pitch_bend_to_segment(
                                stolen_note['original_sample'],
                                stolen_note['input_offset'],
                                output_samples,
                                stolen_note['current_pitch_bend']
                            )

                            if len(pitched_segment) > 0:
                                # Apply quick release envelope for note stealing using optimized function
                                processed_segment = apply_release_envelope_optimized(
                                    pitched_segment, len(pitched_segment), quick_release,
                                    sample_rate, stolen_note['velocity']
                                )
                                output[segment_start:segment_start + len(pitched_segment)] += processed_segment

                    # When disable_release_fade is True, just delete the note immediately without processing
                    channel_notes_count[stolen_note['channel']] -= 1
                    del active_notes[note_to_steal]

                channel_notes_count[event.channel] += 1
                original_sample_data, _ = samples[note]
                
                note_id = next_note_id
                next_note_id += 1

                active_notes[note_id] = {
                    'start': note_start,
                    'segment_start': note_start,
                    'original_sample': original_sample_data,
                    'velocity': velocity,
                    'start_time': event_time,
                    'channel': event.channel,
                    'note': note,
                    'current_pitch_bend': channel_pitch_bend[event.channel],
                    'input_offset': 0,  # Track position in original sample for proper resampling
                    'is_releasing': False
                }

                key = (event.channel, note)
                if key not in note_ids:
                    note_ids[key] = []
                note_ids[key].append(note_id)

        elif event.event_type == 'note_off':
            key = (event.channel, event.param1)
            if key in note_ids and note_ids[key]:
                note_id = note_ids[key].pop(0)
                if note_id in active_notes:
                    note_info = active_notes[note_id]
                    note_info['is_releasing'] = True
                    channel_notes_count[note_info['channel']] -= 1
                    
                    segment_start = note_info['segment_start']
                    note_end = int((event_time - chunk_start_time + release_time) * sample_rate)
                    actual_end = min(note_end, len(output))
                    
                    if actual_end > segment_start:
                        output_samples = actual_end - segment_start

                        # Apply pitch bending using proper resampling
                        pitched_segment = apply_pitch_bend_to_segment(
                            note_info['original_sample'],
                            note_info['input_offset'],
                            output_samples,
                            note_info['current_pitch_bend']
                        )

                        segment_length = min(len(pitched_segment), actual_end - segment_start)
                        if segment_length > 0:
                            if disable_release_fade:
                                # No fade-out, just play the remaining audio
                                output[segment_start:segment_start + segment_length] += \
                                    pitched_segment[:segment_length] * note_info['velocity']
                            else:
                                # Apply release envelope using optimized function
                                processed_segment = apply_release_envelope_optimized(
                                    pitched_segment, segment_length, release_time,
                                    sample_rate, note_info['velocity']
                                )
                                output[segment_start:segment_start + segment_length] += processed_segment
                    
                    del active_notes[note_id]

    # Handle remaining active notes at chunk end
    for note_id, note_info in list(active_notes.items()):
        segment_start = note_info['segment_start']
        actual_end = min(len(output), segment_start + int(release_time * sample_rate))
        
        if actual_end > segment_start:
            output_samples = actual_end - segment_start

            # Apply pitch bending using proper resampling
            pitched_segment = apply_pitch_bend_to_segment(
                note_info['original_sample'],
                note_info['input_offset'],
                output_samples,
                note_info['current_pitch_bend']
            )

            segment_length = min(len(pitched_segment), actual_end - segment_start)
            if segment_length > 0:
                if disable_release_fade:
                    # No fade-out, just play the remaining audio
                    output[segment_start:segment_start + segment_length] += \
                        pitched_segment[:segment_length] * note_info['velocity']
                else:
                    # Apply release envelope using optimized function
                    processed_segment = apply_release_envelope_optimized(
                        pitched_segment, segment_length, release_time,
                        sample_rate, note_info['velocity']
                    )
                    output[segment_start:segment_start + segment_length] += processed_segment

    return output

# --- Sound Effects ---

@njit(fastmath=True)
def apply_simple_reverb(audio, sample_rate, amount):
    """Applies a simple multi-tap feedback reverb effect."""
    if amount <= 0 or audio.ndim != 2: # Ensure stereo
        return audio

    mix = amount / 100.0
    wet_signal = np.zeros_like(audio)
    
    # Define multiple delay taps (lengths in seconds, chosen somewhat arbitrarily)
    # Use slightly different times for L/R channels for stereo effect
    delay_times_l = np.array([0.0297, 0.0371, 0.0411, 0.0437])
    delay_times_r = np.array([0.0313, 0.0389, 0.0423, 0.0451])
    feedback = 0.25 * mix # Lower feedback for multiple taps
    decay = 0.4 * mix    # Overall decay factor

    delay_samples_l = np.array([(int(t * sample_rate)) for t in delay_times_l])
    delay_samples_r = np.array([(int(t * sample_rate)) for t in delay_times_r])
    
    max_delay_l = np.max(delay_samples_l) if len(delay_samples_l) > 0 else 0
    max_delay_r = np.max(delay_samples_r) if len(delay_samples_r) > 0 else 0

    # Process Left Channel
    if max_delay_l > 0:
        # Using a simple circular buffer approach for feedback
        buffer_l = np.zeros(max_delay_l + 1) 
        write_pos_l = 0
        for i in range(len(audio)):
            output_sample = 0.0
            for j in range(len(delay_samples_l)):
                 read_pos = (write_pos_l - delay_samples_l[j] + len(buffer_l)) % len(buffer_l)
                 output_sample += buffer_l[read_pos]
            
            output_sample *= (decay / len(delay_samples_l)) # Average contribution

            # Feedback loop
            current_input = audio[i, 0] + output_sample * feedback
            buffer_l[write_pos_l] = current_input
            wet_signal[i, 0] = output_sample
            write_pos_l = (write_pos_l + 1) % len(buffer_l)


    # Process Right Channel (similar logic, different delays)
    if max_delay_r > 0:
        buffer_r = np.zeros(max_delay_r + 1)
        write_pos_r = 0
        for i in range(len(audio)):
            output_sample = 0.0
            for j in range(len(delay_samples_r)):
                 read_pos = (write_pos_r - delay_samples_r[j] + len(buffer_r)) % len(buffer_r)
                 output_sample += buffer_r[read_pos]

            output_sample *= (decay / len(delay_samples_r))

            current_input = audio[i, 1] + output_sample * feedback
            buffer_r[write_pos_r] = current_input
            wet_signal[i, 1] = output_sample
            write_pos_r = (write_pos_r + 1) % len(buffer_r)


    # Clipping to prevent excessive levels
    for i in range(len(wet_signal)):
        for j in range(2):
            if wet_signal[i, j] > 0.9:
                wet_signal[i, j] = 0.9
            elif wet_signal[i, j] < -0.9:
                wet_signal[i, j] = -0.9

    # Mix dry and wet signals
    result = np.zeros_like(audio)
    for i in range(len(audio)):
        for j in range(2):
            result[i, j] = audio[i, j] * (1.0 - mix) + wet_signal[i, j] * mix
            if result[i, j] > 1.0:
                result[i, j] = 1.0
            elif result[i, j] < -1.0:
                result[i, j] = -1.0

    return result


# Replace the chorus function with this Numba-accelerated version
@njit(fastmath=True)
def apply_simple_chorus(audio, sample_rate, amount):
    """Applies a simple stereo chorus effect."""
    if amount <= 0 or audio.ndim != 2: # Ensure stereo
        return audio

    mix = amount / 100.0
    rate_hz = 0.6     # LFO rate
    depth = 0.002 * sample_rate * mix # Modulation depth in samples, scaled by mix

    wet_signal = np.zeros_like(audio)
    num_samples = len(audio)
    
    # Precompute LFO signals (sine for L, cosine for R for simple stereo phase difference)
    lfo_l = np.zeros(num_samples)
    lfo_r = np.zeros(num_samples)
    
    for i in range(num_samples):
        t = i / sample_rate
        lfo_l[i] = np.sin(2 * np.pi * rate_hz * t) * depth
        lfo_r[i] = np.cos(2 * np.pi * rate_hz * t) * depth

    # Base delay + modulation
    base_delay_samples = 0.005 * sample_rate # 5ms base delay
    
    # Left Channel
    for i in range(num_samples):
        delay_samples = base_delay_samples + lfo_l[i]
        read_index = i - delay_samples
        
        if read_index < 0:
            wet_signal[i, 0] = 0.0  # Simple boundary handling
        else:
            idx_floor = int(read_index)
            idx_ceil = min(num_samples - 1, idx_floor + 1)
            frac = read_index - idx_floor
            
            # Linear interpolation
            if idx_floor >= 0 and idx_floor < num_samples:
                sample_floor = audio[idx_floor, 0]
            else:
                sample_floor = 0.0
                
            if idx_ceil >= 0 and idx_ceil < num_samples:
                sample_ceil = audio[idx_ceil, 0]
            else:
                sample_ceil = 0.0
                
            wet_signal[i, 0] = sample_floor * (1 - frac) + sample_ceil * frac
    
    # Right Channel
    for i in range(num_samples):
        delay_samples = base_delay_samples + lfo_r[i]
        read_index = i - delay_samples
        
        if read_index < 0:
            wet_signal[i, 1] = 0.0  # Simple boundary handling
        else:
            idx_floor = int(read_index)
            idx_ceil = min(num_samples - 1, idx_floor + 1)
            frac = read_index - idx_floor
            
            # Linear interpolation
            if idx_floor >= 0 and idx_floor < num_samples:
                sample_floor = audio[idx_floor, 1]
            else:
                sample_floor = 0.0
                
            if idx_ceil >= 0 and idx_ceil < num_samples:
                sample_ceil = audio[idx_ceil, 1]
            else:
                sample_ceil = 0.0
                
            wet_signal[i, 1] = sample_floor * (1 - frac) + sample_ceil * frac

    # Clipping
    for i in range(len(wet_signal)):
        for j in range(2):
            if wet_signal[i, j] > 0.9:
                wet_signal[i, j] = 0.9
            elif wet_signal[i, j] < -0.9:
                wet_signal[i, j] = -0.9
    
    # Mix dry and wet signals
    result = np.zeros_like(audio)
    for i in range(len(audio)):
        for j in range(2):
            result[i, j] = audio[i, j] * (1.0 - mix) + wet_signal[i, j] * mix
            if result[i, j] > 1.0:
                result[i, j] = 1.0
            elif result[i, j] < -1.0:
                result[i, j] = -1.0

    return result

# --- Normalization ---

def smooth_normalize(audio: np.ndarray, window_size: int, target_peak: float, target_rms: float,
                    attack: float, release: float, max_boost_db: float, max_cut_db: float,
                    previous_level: float = None) -> Tuple[np.ndarray, float]:
    # Convert dB values to linear scale
    target_peak_linear = 10 ** (target_peak / 20)
    target_rms_linear = 10 ** (target_rms / 20)
    max_boost_linear = 10 ** (max_boost_db / 20)
    max_cut_linear = 10 ** (max_cut_db / 20)
    
    # Downsampling for faster processing
    downsample_factor = max(1, min(20, window_size // 50))
    downsampled_audio = audio[::downsample_factor]
    
    # Calculate RMS and peak levels
    if downsampled_audio.ndim > 1:
        rms = np.sqrt(np.mean(downsampled_audio**2, axis=1))
        peak = np.max(np.abs(downsampled_audio), axis=1)
    else:
        rms = np.sqrt(np.mean(downsampled_audio**2))
        peak = np.max(np.abs(downsampled_audio))
    # Combine RMS and peak information
    level = np.maximum(rms / target_rms_linear, peak / target_peak_linear)
    
    if previous_level is not None:
        level = np.maximum(level[0], previous_level)
    
    # Apply attack and release
    attack_coeff = np.exp(-1 / (attack * window_size / downsample_factor))
    release_coeff = np.exp(-1 / (release * window_size / downsample_factor))
    
    forward_smooth = signal.lfilter([1 - attack_coeff], [1, -attack_coeff], level)
    backward_smooth = signal.lfilter([1 - release_coeff], [1, -release_coeff], level[::-1])[::-1]
    
    smoothed_level = np.maximum(forward_smooth, backward_smooth)
    smoothed_level = np.maximum(smoothed_level, 1e-8)
    
    # Calculate scaling factors with limits
    scaling_factors = np.clip(1 / smoothed_level, max_cut_linear, max_boost_linear)
    
    # Upsample scaling factors
    smoothed_factors = np.interp(np.arange(len(audio)),
                                np.linspace(0, len(audio) - 1, len(scaling_factors)),
                                scaling_factors)
    
    if audio.ndim > 1:
        smoothed_factors = smoothed_factors.reshape(-1, 1)
    
    normalized_audio = audio * smoothed_factors
    
    return normalized_audio, smoothed_level[-1]
    
def process_chunk(chunk_data: Tuple[int, float, float, List[bytes], Dict, int, List[Tuple[int, int]], int, float, float,
                                  float, float, float, float, float, float, float, Queue, bool, int, float, float, float]) -> Tuple[np.ndarray, float]: # Added reverb/chorus amounts
    (chunk_index, chunk_start, chunk_end, tracks, samples, ticks_per_beat, tempo_changes, sample_rate,
     polyphony_limit, release_time, target_peak, target_rms, attack, release, max_boost_db, max_cut_db,
     previous_level, message_queue, disable_release_fade,
     processing_fps, fps_fluctuation, reverb_amount, chorus_amount) = chunk_data # Added reverb/chorus unpacking
    
    chunk_duration = chunk_end - chunk_start
    output = np.zeros((int(chunk_duration * sample_rate), 2), dtype=np.float32)
    
    
    for i, track in enumerate(tracks):
        # First processing track should add to log
        if i == 0:
            message_queue.put(f"Processing chunk {chunk_index + 1}, track {i + 1}/{len(tracks)} [{0:>3.1f}%]")
    
        # Process track
        # When disable_release_fade is False, samples are pre-processed with fade-out
        track_audio = process_track_chunk(
            track, samples, ticks_per_beat, tempo_changes, sample_rate,
            chunk_start, chunk_end, polyphony_limit, release_time,
            processing_fps, fps_fluctuation, # Added
            disable_release_fade
        )
        output += track_audio
    
        # Update progress after processing each track
        percentage = ((i + 1) / len(tracks)) * 100
        message_queue.put(f"Processing chunk {chunk_index + 1}, track {i + 1}/{len(tracks)} [{percentage:>3.1f}%]")
    
    # Apply effects before normalization
    if reverb_amount > 0:
        output = apply_simple_reverb(output, sample_rate, reverb_amount)
    if chorus_amount > 0:
        output = apply_simple_chorus(output, sample_rate, chorus_amount)

    window_duration = 0.01
    window_size = int(window_duration * sample_rate)
    
    normalized_output, new_level = smooth_normalize(
        output, window_size, target_peak, target_rms,
        attack, release, max_boost_db, max_cut_db,
        previous_level
    )
    
    return normalized_output, new_level

def render_midi_to_audio(midi_path: str, samples: Dict[int, Tuple[np.ndarray, int]], output_path: str,
                        chunk_duration: float, polyphony_limit: int, release_time: float,
                        target_peak: float, target_rms: float, attack: float, release: float,
                        max_boost_db: float, max_cut_db: float, message_queue: Queue,
                        disable_release_fade: bool = False,
                        processing_fps: int = 0, fps_fluctuation: float = 0.0,
                        reverb_amount: float = 0.0, chorus_amount: float = 0.0): # Added effects params

    message_queue.put("Starting MIDI rendering process...")

    # Initialize pre-computed envelopes for performance optimization
    if not disable_release_fade:
        message_queue.put("Pre-computing release envelopes for optimization...")
        sample_rate = next(iter(samples.values()))[1]  # Get sample rate from first sample
        _initialize_precomputed_envelopes(release_time, sample_rate)
    
    ticks_per_beat, num_tracks, tracks, tempo_changes = parse_midi_file(
        midi_path, 
        lambda desc, cur, tot: message_queue.put(f"{desc}: {cur}/{tot}")
    )

    target_sample_rate = 44100  # Force output sample rate
    message_queue.put(f"Target sample rate set to: {target_sample_rate} Hz")

    # Resample all loaded samples to the target rate
    resampled_samples = {}
    num_samples_to_resample = len(samples)
    resampled_count = 0
    message_queue.put(f"Resampling {num_samples_to_resample} samples to {target_sample_rate} Hz...")
    for note, (sample_data, original_rate) in samples.items():
        if original_rate != target_sample_rate:
            try:
                num_samples_new = int(len(sample_data) * target_sample_rate / original_rate)
                # Ensure stereo if needed before resampling
                if sample_data.ndim == 1:
                    sample_data = np.column_stack((sample_data, sample_data))
                resampled_data = signal.resample(sample_data, num_samples_new, axis=0)
                # Ensure output is float32
                resampled_samples[note] = (resampled_data.astype(np.float32), target_sample_rate)
            except Exception as e:
                message_queue.put(f"Warning: Could not resample sample for note {note} from {original_rate} Hz: {e}")
                # Optionally skip this sample or use original if compatible? For now, skip.
                continue
        else:
            # Ensure output is float32 even if not resampled
            resampled_samples[note] = (sample_data.astype(np.float32), original_rate)

        resampled_count += 1
        if resampled_count % max(1, num_samples_to_resample // 10) == 0: # Update progress periodically
             message_queue.put(f"Resampling progress: {resampled_count}/{num_samples_to_resample}")

    message_queue.put(f"Finished resampling. Usable samples: {len(resampled_samples)}")

    if not resampled_samples:
        message_queue.put("Error: No usable samples after resampling.")
        return # Stop processing if no samples are left

    # Use the resampled samples dictionary from now on
    samples = resampled_samples
    sample_rate = target_sample_rate # Use the target rate for all subsequent calculations

    max_time = max(max(event.time for event in parse_midi_events(track)) for track in tracks)
    total_seconds = sum(tick2second(tempo_changes[i+1][0] - tempo_changes[i][0], 
                                  ticks_per_beat, tempo_changes[i][1])
                       for i in range(len(tempo_changes) - 1))
    total_seconds += tick2second(max_time - tempo_changes[-1][0], 
                               ticks_per_beat, tempo_changes[-1][1])
    
    message_queue.put(f"Sample rate: {sample_rate} Hz")
    message_queue.put(f"Chunk duration: {chunk_duration} seconds")
    message_queue.put(f"Number of tracks: {num_tracks}")
    message_queue.put(f"PPQ: {ticks_per_beat}")
    message_queue.put(f"MIDI duration: ~{int(total_seconds)} seconds "
                     f"({int(total_seconds/60)}:{int(total_seconds%60):02d})")
    
    num_chunks = int(np.ceil(total_seconds / chunk_duration))
    message_queue.put(f"Number of chunks: {num_chunks}")
    
    with sf.SoundFile(output_path, mode='w', samplerate=sample_rate, channels=2) as output_file:
        previous_level = None
        
        for chunk_index in range(num_chunks):
            chunk_start = chunk_index * chunk_duration
            chunk_end = min((chunk_index + 1) * chunk_duration, total_seconds)
            
            message_queue.put(f"Processing chunk {chunk_index + 1}/{num_chunks}")
            
            chunk_data = (
                chunk_index, chunk_start, chunk_end, tracks, samples, ticks_per_beat,
                tempo_changes, sample_rate, polyphony_limit, release_time,
                target_peak, target_rms, attack, release, max_boost_db, max_cut_db,
                previous_level, message_queue, disable_release_fade,
                processing_fps, fps_fluctuation, # Added
                reverb_amount, chorus_amount # Added
            )
            
            current_chunk, new_level = process_chunk(chunk_data)
            previous_level = new_level
            
            output_file.write(current_chunk)
            
    message_queue.put(f"Rendered audio saved to {output_path}")

if __name__ == "__main__":
    multiprocessing.freeze_support()
    gui = MIDIRendererGUI()
    gui.run()